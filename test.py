import os
import sys
import warnings
import numpy as np

# 修复NumPy初始化问题
os.environ['NUMPY_NO_ARRAY_API'] = '1'
np.__config__.show()

# 屏蔽所有警告
warnings.filterwarnings("ignore")

def check_environment():
    try:
        import pyarrow
        print(f"pyarrow版本: {pyarrow.__version__}")  # 应为11.0.0
        return True
    except Exception as e:
        print(f"环境检查失败: {str(e)}")
        return False

if check_environment():
    from modelscope.pipelines import pipeline
    from modelscope.utils.constant import Tasks

    def load_model():
        model_path = r"E:\ct_transformer\models\iic\punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
        try:
            return pipeline(
                task=Tasks.punctuation,
                model=model_path,
                device='cpu'
            )
        except Exception as e:
            print(f"模型加载失败: {str(e)}")
            return None

    if __name__ == "__main__":
        print("正在初始化模型...")
        punc_model = load_model()
        +if punc_model:
            test_texts = [
                "今天天气真好",
                "这是一个测试例子希望模型能正确添加标点", 
                "请问去北京怎么走"
            ]
            print("\n测试结果:")
            for text in test_texts:
                result = punc_model(text)
                print(f"输入: {text}")
                print(f"输出: {result}\n")
